<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="14c22bee-40c7-4bdf-8d79-8049269330bf" name="Changes" comment="ai对话的prompt修复">
      <change beforePath="$PROJECT_DIR$/src/main/java/cn/sdtbu/edu/config/CorsConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/cn/sdtbu/edu/config/CorsConfig.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;Justinxiaohao&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/Justinxiaohao/Spring-AI-Course.git&quot;,
    &quot;accountId&quot;: &quot;475e4154-f7ae-4e2e-96c2-9b8702dc1446&quot;
  }
}</component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.9.9" />
        <option name="localRepository" value="D:\maven\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2xZp5cmlt08QqOb3UMsSz6xga4W" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.Gena.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.UploadControllerTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.UserProgramLikeServiceTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.UserServiceEmailTest.executor&quot;: &quot;Run&quot;,
    &quot;Maven.spring-ai [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.spring-ai [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.SpringAiApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/CodeProject/javacode/Spring-AI-Course/spring-ai&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;pProject Default&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;,
      &quot;mysql_aurora&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\CodeProject\javacode\spring-ai\src\main\java\cn\sdtbu\edu\utils" />
      <recent name="D:\CodeProject\javacode\spring-ai\src\main\java\cn\sdtbu\edu" />
      <recent name="D:\CodeProject\javacode\spring-ai\src\main\java\cn\sdtbu\edu\repo" />
      <recent name="D:\CodeProject\javacode\spring-ai\src\main\java\cn\sdtbu\edu\service" />
      <recent name="D:\CodeProject\javacode\spring-ai\src\main\java\cn\sdtbu\edu\entity" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="cn.sdtbu.edu" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.SpringAiApplication">
    <configuration name="Gena" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="cn.sdtbu.edu.Gena" />
      <module name="spring-ai" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UploadControllerTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="spring-ai" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.sdtbu.edu.controller.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="cn.sdtbu.edu.controller" />
      <option name="MAIN_CLASS_NAME" value="cn.sdtbu.edu.controller.UploadControllerTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserProgramLikeServiceTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="spring-ai" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.sdtbu.edu.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="cn.sdtbu.edu.service" />
      <option name="MAIN_CLASS_NAME" value="cn.sdtbu.edu.service.UserProgramLikeServiceTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceEmailTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="spring-ai" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.sdtbu.edu.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="cn.sdtbu.edu.service" />
      <option name="MAIN_CLASS_NAME" value="cn.sdtbu.edu.service.UserServiceEmailTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SpringAiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="spring-ai" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.sdtbu.edu.SpringAiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.UploadControllerTest" />
        <item itemvalue="JUnit.UserServiceEmailTest" />
        <item itemvalue="JUnit.UserProgramLikeServiceTest" />
        <item itemvalue="Application.Gena" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="14c22bee-40c7-4bdf-8d79-8049269330bf" name="Changes" comment="" />
      <created>1748155269164</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748155269164</updated>
      <workItem from="1748155270330" duration="5115000" />
      <workItem from="1748221567075" duration="1664000" />
      <workItem from="1748224209393" duration="1400000" />
      <workItem from="1748232961188" duration="1104000" />
      <workItem from="1748238899603" duration="1905000" />
      <workItem from="1748248507256" duration="10444000" />
      <workItem from="1748263119601" duration="1039000" />
      <workItem from="1748265373694" duration="1316000" />
      <workItem from="1748305789384" duration="3046000" />
      <workItem from="1748308908593" duration="12016000" />
      <workItem from="1748341347813" duration="825000" />
      <workItem from="1748348771975" duration="2591000" />
      <workItem from="1748410762911" duration="1838000" />
      <workItem from="1748413287223" duration="5535000" />
      <workItem from="1748438860005" duration="2416000" />
      <workItem from="1748482172725" duration="1617000" />
      <workItem from="1748487183028" duration="1687000" />
      <workItem from="1748491947801" duration="10355000" />
      <workItem from="1748566134211" duration="413000" />
      <workItem from="1748578762190" duration="18248000" />
      <workItem from="1748660635609" duration="6888000" />
      <workItem from="1748676011472" duration="886000" />
      <workItem from="1748677151420" duration="1099000" />
      <workItem from="1748678748222" duration="401000" />
      <workItem from="1748680498881" duration="1361000" />
      <workItem from="1748750753199" duration="1221000" />
      <workItem from="1748753877468" duration="3846000" />
      <workItem from="1748763638641" duration="2681000" />
      <workItem from="1748776604456" duration="1490000" />
    </task>
    <task id="LOCAL-00001" summary="报错修复">
      <option name="closed" value="true" />
      <created>1748306239223</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1748306239223</updated>
    </task>
    <task id="LOCAL-00002" summary="模型参数配置">
      <option name="closed" value="true" />
      <created>1748309406265</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1748309406265</updated>
    </task>
    <task id="LOCAL-00003" summary="模型参数配置">
      <option name="closed" value="true" />
      <created>1748318821732</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1748318821732</updated>
    </task>
    <task id="LOCAL-00004" summary="电台短信">
      <option name="closed" value="true" />
      <created>1748319580143</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1748319580143</updated>
    </task>
    <task id="LOCAL-00005" summary="修复密码加密的错误">
      <option name="closed" value="true" />
      <created>1748327351712</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1748327351712</updated>
    </task>
    <task id="LOCAL-00006" summary="修复密码加密的错误">
      <option name="closed" value="true" />
      <created>1748328923804</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1748328923804</updated>
    </task>
    <task id="LOCAL-00007" summary="更新">
      <option name="closed" value="true" />
      <created>1748579123823</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1748579123823</updated>
    </task>
    <task id="LOCAL-00008" summary="更换头像功能修复">
      <option name="closed" value="true" />
      <created>1748585201689</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1748585201689</updated>
    </task>
    <task id="LOCAL-00009" summary="更换头像功能修复">
      <option name="closed" value="true" />
      <created>1748611556825</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1748611556825</updated>
    </task>
    <task id="LOCAL-00010" summary="功能修复">
      <option name="closed" value="true" />
      <created>1748660690149</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1748660690149</updated>
    </task>
    <task id="LOCAL-00011" summary="功能修复">
      <option name="closed" value="true" />
      <created>1748677165935</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1748677165935</updated>
    </task>
    <task id="LOCAL-00012" summary="ai对话的prompt修复">
      <option name="closed" value="true" />
      <created>1748678231371</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1748678231371</updated>
    </task>
    <option name="localTasksCounter" value="13" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="springai项目第一次重构提交" />
    <MESSAGE value="报错修复" />
    <MESSAGE value="模型参数配置" />
    <MESSAGE value="电台短信" />
    <MESSAGE value="修复密码加密的错误" />
    <MESSAGE value="更新" />
    <MESSAGE value="更换头像功能修复" />
    <MESSAGE value="功能修复" />
    <MESSAGE value="ai对话的prompt修复" />
    <option name="LAST_COMMIT_MESSAGE" value="ai对话的prompt修复" />
  </component>
</project>